from typing import Optional
from uuid import uuid4
from src.utils.log_util import logger
from src.client.database.activity_client import activity_client
from src.client.database.user_wallet_client import user_wallet_client
from src.common.session import get_session
from src.models.database.activity import ActivityType
from src.models.database.activity_record import ActivityRecord
from src.models.database.yuxucoin_transaction import (
    YuxuCoinTransaction,
    YuxuCoinTransactionRelatedEntityType,
    YuxuCoinTransactionType,
)
from src.models.activity_model import ActivityCompositeResponse, ActivityQueryRequest
from fastapi_pagination import Page, Params


class ActivityService:

    async def give_first_register_gift(self, user_id: str) -> Optional[int]:
        activity = await activity_client.get_activity_by_type(
            ActivityType.FIRST_REGISTER_GIFT
        )
        if not activity:
            logger.info("First register gift activity not found")
            return None
        # 现阶段写死为发放玉虚币
        yuxu_coin_reward = int(activity.rewards.get("YUXU_COIN", 0))
        if yuxu_coin_reward <= 0:
            logger.info("First register gift activity has no yuxu coin reward")
            return None

        # 发放玉虚币到用户钱包
        async with get_session() as session:
            user_wallet = await user_wallet_client.get_user_wallet_with_lock(
                session=session, user_id=user_id
            )
            if not user_wallet:
                logger.info("User wallet not found")
                return None

            yuxu_coin_transaction = YuxuCoinTransaction(
                user_id=user_id,
                transaction_log_id=uuid4(),
                amount=yuxu_coin_reward,
                transaction_type=YuxuCoinTransactionType.GIVE.value,
                wallet_id=user_wallet.wallet_id,
                balance_before=user_wallet.balance,
                balance_after=user_wallet.balance + yuxu_coin_reward,
                related_entity_type=YuxuCoinTransactionRelatedEntityType.ACTIVITY.value,
                related_entity_id=activity.activity_id,
                description=f"First register gift activity has been given to user {user_id}",
            )
            user_wallet.balance += yuxu_coin_reward
            activity_record = ActivityRecord(
                user_id=user_id,
                user_activity_id=uuid4(),
                activity_id=activity.activity_id,
                status="SUCCESS",
                reward_details=activity.rewards,
                notes=f"First register gift activity has been given to user {user_id}",
            )
            session.add(activity_record)
            session.add(user_wallet)
            session.add(yuxu_coin_transaction)
            await session.commit()
            logger.info(
                f"First register gift yuxu_coin {yuxu_coin_reward} activity has been given to user {user_id}"
            )
            return yuxu_coin_reward

    async def get_user_activities(
        self,
        user_id: str,
        query_request: ActivityQueryRequest,
        page_params: Optional[Params] = None,
    ) -> Page[ActivityCompositeResponse]:
        """
        Get user activities with participation status using fastapi-pagination

        Args:
            user_id: User ID to get activities for
            query_request: ActivityQueryRequest containing filter parameters
            page_params: Optional pagination parameters (page, size)

        Returns:
            Page[ActivityCompositeResponse] with paginated activity data
        """
        # Use activity client to get paginated data
        page_result = await activity_client.get_user_activities_paginated(
            user_id=user_id, query_request=query_request, page_params=page_params
        )

        # Transform the items using ActivityCompositeResponse transformer
        # page_result.items now contains tuples of (Activity, Optional[ActivityRecord])
        transformed_items = [
            ActivityCompositeResponse.transform_activity_record_tuple(
                activity, activity_record
            )
            for activity, activity_record in page_result.items
        ]

        # Return a new Page with transformed items
        return Page(
            items=transformed_items,
            total=page_result.total,
            page=page_result.page,
            size=page_result.size,
            pages=page_result.pages,
        )


activity_service = ActivityService()
