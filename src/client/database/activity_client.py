from typing import Optional, Any, <PERSON><PERSON>

from sqlalchemy import select, and_, func
from fastapi_pagination import Page, Params
from src.common.session import get_session
from src.common.pagination_utils import paginate_query
from src.models.database.activity import Activity, ActivityStatus, ActivityType
from src.models.database.activity_record import ActivityRecord
from src.models.activity_model import ActivityQueryRequest


class ActivityClient:
    _instance = None

    def __new__(cls) -> "ActivityClient":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def get_activity_by_type(
        self, activity_type: ActivityType
    ) -> Optional[Activity]:
        async with get_session() as session:
            result = await session.execute(
                select(Activity)
                .where(
                    Activity.activity_type == activity_type.value,
                    Activity.status == ActivityStatus.ACTIVE.value,
                    Activity.start_time <= func.now(),
                    Activity.end_time >= func.now(),
                )
                .order_by(Activity.start_time.desc())
            )
            return result.scalars().first()

    def get_activities_with_user_records_query(
        self, user_id: str, query_request: ActivityQueryRequest
    ) -> Any:
        """
        Build a query for activities with user participation records
        This query will be used with fastapi-pagination's paginate function

        Args:
            user_id: User ID to get records for
            query_request: ActivityQueryRequest containing filter parameters

        Returns:
            SQLAlchemy select query for activities with user records
        """
        # Build base query with left join
        base_query = select(Activity, ActivityRecord).outerjoin(
            ActivityRecord,
            and_(
                Activity.activity_id == ActivityRecord.activity_id,
                ActivityRecord.user_id == user_id,
            ),
        )

        # Apply activity status filter if provided
        activity_statuses = query_request.get_activity_statuses()
        if activity_statuses:
            base_query = base_query.where(Activity.status.in_(activity_statuses))

        # Apply activity type filter if provided
        activity_types = query_request.get_activity_types()
        if activity_types:
            base_query = base_query.where(Activity.activity_type.in_(activity_types))

        # Apply activity ID filter if provided
        if query_request.activity_id:
            base_query = base_query.where(
                Activity.activity_id == query_request.activity_id
            )

        # Apply user-specified time range filters (independent of activity status)
        if query_request.activity_start_time_gt:
            base_query = base_query.where(
                Activity.start_time > query_request.activity_start_time_gt
            )

        if query_request.activity_start_time_lt:
            base_query = base_query.where(
                Activity.start_time < query_request.activity_start_time_lt
            )

        if query_request.activity_end_time_gt:
            base_query = base_query.where(
                Activity.end_time > query_request.activity_end_time_gt
            )

        if query_request.activity_end_time_lt:
            base_query = base_query.where(
                Activity.end_time < query_request.activity_end_time_lt
            )

        # Order by activity start time descending
        base_query = base_query.order_by(Activity.start_time.desc())

        return base_query

    async def get_user_activities_paginated(
        self,
        user_id: str,
        query_request: ActivityQueryRequest,
        page_params: Optional[Params] = None,
    ) -> Page[Tuple[Activity, Optional[ActivityRecord]]]:
        """
        Get user activities with participation status using fastapi-pagination

        Args:
            user_id: User ID to get activities for
            query_request: ActivityQueryRequest containing filter parameters
            page_params: Optional pagination parameters (page, size)

        Returns:
            Page with paginated tuples of (Activity, ActivityRecord)
        """
        # Get the query for activities with user records
        query = self.get_activities_with_user_records_query(
            user_id=user_id, query_request=query_request
        )

        # Use common pagination utility for manual pagination
        async with get_session() as session:
            return await paginate_query(
                session=session, query=query, params=page_params
            )


activity_client = ActivityClient()
