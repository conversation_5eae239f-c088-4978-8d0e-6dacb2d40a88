from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field

from src.models.database.activity import Activity, ActivityStatus
from src.models.database.activity_record import ActivityRecord


class ActivityQueryRequest(BaseModel):
    """Request model for activity query parameters"""

    activity_status: Optional[str] = Field(
        None,
        description="Filter activities by status (comma-separated for multiple statuses, e.g., 'ACTIVE,PLANNED' or 'EXPIRED,ARCHIVED')",
    )
    activity_type: Optional[str] = Field(
        None,
        description="Filter activities by type (comma-separated for multiple types, e.g., 'FIRST_REGISTER_GIFT,REGULAR')",
    )
    activity_id: Optional[str] = Field(
        None, description="Filter by specific activity ID"
    )

    # Time range filtering fields
    activity_start_time_gt: Optional[datetime] = Field(
        None,
        description="Filter activities that start after this time (e.g., '2025-01-01T00:00:00')",
    )
    activity_start_time_lt: Optional[datetime] = Field(
        None,
        description="Filter activities that start before this time (e.g., '2025-12-31T23:59:59')",
    )
    activity_end_time_gt: Optional[datetime] = Field(
        None,
        description="Filter activities that end after this time (e.g., '2025-01-01T00:00:00')",
    )
    activity_end_time_lt: Optional[datetime] = Field(
        None,
        description="Filter activities that end before this time (e.g., '2025-12-31T23:59:59')",
    )

    def get_activity_types(self) -> Optional[List[str]]:
        """Parse comma-separated activity types into a list"""
        if not self.activity_type:
            return None
        return [t.strip() for t in self.activity_type.split(",") if t.strip()]

    def get_activity_statuses(self) -> Optional[List[str]]:
        """Parse comma-separated activity statuses into a list"""
        if not self.activity_status:
            return None
        return [s.strip() for s in self.activity_status.split(",") if s.strip()]


class ActivityCompositeResponse(BaseModel):
    """Response model for activity data combined with user participation record"""

    # Activity fields
    activity_id: str
    activity_name: str
    description: Optional[str] = None
    activity_type: Optional[str] = None
    activity_start_time: datetime
    activity_end_time: Optional[datetime] = None
    activity_status: ActivityStatus
    rules: Optional[dict] = None
    rewards: Optional[dict] = None

    # User activity record fields (optional - only present if user participated)
    user_id: Optional[str] = None
    user_activity_id: Optional[str] = None
    activity_record_status: Optional[str] = None
    reward_details: Optional[dict] = None
    notes: Optional[str] = None
    extra_info: Optional[dict] = None
    record_created_at: Optional[datetime] = None
    record_updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

    @classmethod
    def transform_activity_record_tuple(
        cls, activity: Activity, activity_record: Optional[ActivityRecord]
    ) -> "ActivityCompositeResponse":
        """
        Transform a tuple of (Activity, ActivityRecord) into ActivityResponse

        Args:
            activity: Activity instance
            activity_record: Optional[ActivityRecord] instance

        Returns:
            ActivityCompositeResponse object
        """
        rules_value = activity.rules if activity.rules is not None else None
        rewards_value = activity.rewards if activity.rewards is not None else None

        return cls(
            # Activity fields
            activity_id=activity.activity_id,
            activity_name=activity.activity_name,
            description=activity.description,
            activity_type=activity.activity_type,
            activity_start_time=activity.start_time,
            activity_end_time=activity.end_time,
            activity_status=ActivityStatus(activity.status),
            rules=rules_value,
            rewards=rewards_value,
            # Activity record fields (if user participated)
            user_id=activity_record.user_id if activity_record else None,
            user_activity_id=(
                activity_record.user_activity_id if activity_record else None
            ),
            activity_record_status=activity_record.status if activity_record else None,
            reward_details=(
                activity_record.reward_details
                if activity_record and activity_record.reward_details
                else None
            ),
            notes=activity_record.notes if activity_record else None,
            extra_info=(
                activity_record.extra_info
                if activity_record and activity_record.extra_info
                else None
            ),
            record_created_at=activity_record.created_at if activity_record else None,
            record_updated_at=activity_record.updated_at if activity_record else None,
        )
