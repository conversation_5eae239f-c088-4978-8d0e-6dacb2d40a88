from datetime import datetime
from enum import Enum
from sqlalchemy import Integer, String, TIMESTAMP, JSON, TEXT
from sqlalchemy.orm import Mapped, mapped_column
from typing import Optional
from src.models.database.base import Base


class Activity(Base):
    __tablename__ = "activity"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    activity_id: Mapped[str] = mapped_column(String(36), unique=True, nullable=False)
    activity_name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(TEXT, nullable=True)
    activity_type: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True, index=True
    )
    start_time: Mapped[datetime] = mapped_column(TIMESTAMP, nullable=False)
    end_time: Mapped[Optional[datetime]] = mapped_column(TIMESTAMP, nullable=True)
    status: Mapped[str] = mapped_column(
        String(50), nullable=False, default="PLANNED", index=True
    )
    rules: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    rewards: Mapped[dict] = mapped_column(JSON, nullable=False)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(
        TIMESTAMP, default=datetime.now, onupdate=datetime.now
    )


class ActivityType(Enum):
    FIRST_REGISTER_GIFT = "FIRST_REGISTER_GIFT"


class ActivityStatus(Enum):
    PLANNED = "PLANNED"
    ACTIVE = "ACTIVE"
    EXPIRED = "EXPIRED"
