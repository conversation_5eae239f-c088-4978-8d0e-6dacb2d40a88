from datetime import datetime
from sqlalchemy import String, DATETIME, <PERSON>IGINT, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Mapped, mapped_column
from typing import Optional
from src.models.database.base import Base


class ActivityRecord(Base):
    __tablename__ = "activity_record"

    id: Mapped[int] = mapped_column(BIGINT, primary_key=True, autoincrement=True)
    user_activity_id: Mapped[str] = mapped_column(
        String(36), unique=True, nullable=False
    )
    user_id: Mapped[str] = mapped_column(String(36), nullable=False, index=True)
    activity_id: Mapped[str] = mapped_column(String(36), nullable=False, index=True)
    status: Mapped[str] = mapped_column(String(50), nullable=False)
    reward_details: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    notes: Mapped[Optional[str]] = mapped_column(String(256), nullable=True)
    extra_info: Mapped[Optional[dict]] = mapped_column(JSON, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DATETIME, default=datetime.now)
    updated_at: Mapped[datetime] = mapped_column(
        DATETIME, default=datetime.now, onupdate=datetime.now
    )
