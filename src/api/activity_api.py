from fastapi import APIRouter, Depends
from fastapi_pagination import Page, Params

from src.common.auth import JWTA<PERSON>
from src.common.decorators import handle_exceptions
from src.models.activity_model import ActivityCompositeResponse, ActivityQueryRequest
from src.services.activity_service import activity_service
from src.utils.log_util import logger


# Create router without v1 prefix since it's handled by main.py
router = APIRouter(prefix="/activity", tags=["Activity"])


@router.get("", response_model=Page[ActivityCompositeResponse])
@handle_exceptions(error_message="Failed to get user activities")
async def get_user_activities(
    query_request: ActivityQueryRequest = Depends(),
    page_params: Params = Depends(),
    current_user_id: str = Depends(JWTAuth.get_current_user_id),
) -> Page[ActivityCompositeResponse]:
    """
    Get user activities with participation status using fastapi-pagination

    This endpoint displays the current user's activity participation status in the mobile app.
    It queries activities and the user's participation records, returning combined data with pagination.

    Query Parameters:
    - activity_status (optional): Filter activities by status. Supports comma-separated values for multiple statuses
      (e.g., 'ACTIVE', 'PLANNED', 'ACTIVE,PLANNED', or 'EXPIRED,ARCHIVED')
    - activity_type (optional): Filter activities by type. Supports comma-separated values for multiple types
      (e.g., 'FIRST_REGISTER_GIFT' or 'FIRST_REGISTER_GIFT,REGULAR')
    - activity_id (optional): Filter by specific activity ID
    - activity_start_time_gt (optional): Filter activities that start after this time (ISO format: '2025-01-01T00:00:00')
    - activity_start_time_lt (optional): Filter activities that start before this time (ISO format: '2025-12-31T23:59:59')
    - activity_end_time_gt (optional): Filter activities that end after this time (ISO format: '2025-01-01T00:00:00')
    - activity_end_time_lt (optional): Filter activities that end before this time (ISO format: '2025-12-31T23:59:59')
    - page (optional): Page number for pagination (default: 1, minimum: 1)
    - size (optional): Number of items per page (default: 50, range: 1-100)

    Time Filtering Notes:
    1. Automatic time filtering: Applied when ANY of the requested statuses is 'ACTIVE' (filters for currently active activities)
    2. User-specified time range filtering: Applied independently using the time range parameters above
    3. Both types of filtering can be used together for precise time-based queries

    Headers:
    - Authorization: Bearer token required

    Returns:
    - items: List of activities with user participation status
    - total: Total number of items
    - page: Current page number
    - size: Items per page
    - pages: Total number of pages
    """
    # Get user activities from service with structured request and pagination parameters
    result = await activity_service.get_user_activities(
        user_id=current_user_id,
        query_request=query_request,
        page_params=page_params,
    )

    logger.info(f"Successfully retrieved activities for user {current_user_id}")
    return result
