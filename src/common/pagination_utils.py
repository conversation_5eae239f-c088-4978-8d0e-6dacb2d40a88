"""
Common pagination utilities for manual pagination with SQLAlchemy queries.

This module provides reusable pagination functionality for complex SQLAlchemy queries
that cannot use fastapi-pagination's automatic validation due to tuple results,
joined queries, or custom transformation requirements.
"""

from typing import TypeVar, Generic, Tuple, Any, Optional, Callable
from sqlalchemy import select, func
from sqlalchemy.engine import Row
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import Select
from fastapi_pagination import Page, Params

# Generic type for pagination results
T = TypeVar("T")


class ManualPaginator(Generic[T]):
    """
    Generic manual paginator for SQLAlchemy queries that return complex results.

    This paginator handles:
    - Total count calculation using subqueries
    - Offset and limit application based on Params
    - Raw SQLAlchemy result extraction
    - Page metadata calculation
    - Proper Page[T] typing

    Use this when fastapi-pagination's automatic validation doesn't work with
    your query structure (e.g., joined queries returning tuples).
    """

    @staticmethod
    async def paginate(
        session: AsyncSession,
        query: Select,
        params: Optional[Params] = None,
        result_transformer: Optional[Callable] = None,
    ) -> Page[T]:
        """
        Manually paginate a SQLAlchemy query with proper type safety.

        Args:
            session: AsyncSession for database operations
            query: SQLAlchemy Select query to paginate
            params: Pagination parameters (page, size). Defaults to Params() if None
            result_transformer: Optional function to transform raw rows before returning.
                               If None, returns raw tuples from fetchall()

        Returns:
            Page[T] with paginated results and metadata

        Example:
            # Basic usage with tuple results
            query = select(User, Profile).outerjoin(Profile, User.id == Profile.user_id)
            page = await ManualPaginator.paginate(session, query)
            # Returns: Page[Tuple[User, Optional[Profile]]]

            # With custom transformer
            def transform_row(row):
                return (row[0], row[1])  # Extract specific columns

            page = await ManualPaginator.paginate(
                session, query, transformer=transform_row
            )
        """
        # Use provided params or create default ones
        if params is None:
            params = Params()

        # Get total count using subquery approach
        count_query = select(func.count()).select_from(query.subquery())
        total_result = await session.execute(count_query)
        total = total_result.scalar() or 0

        # Apply pagination to the query
        offset = (params.page - 1) * params.size
        paginated_query = query.offset(offset).limit(params.size)

        # Execute the paginated query
        result = await session.execute(paginated_query)
        raw_rows = result.fetchall()

        # Transform results if transformer provided, otherwise return raw tuples
        if result_transformer:
            items = [result_transformer(row) for row in raw_rows]
        else:
            # Default transformation: convert Row objects to tuples
            items = [tuple(row) for row in raw_rows]

        # Calculate pagination metadata
        pages = (total + params.size - 1) // params.size if total > 0 else 1

        # Return Page object with proper typing
        return Page[T](
            items=items,
            total=total,
            page=params.page,
            size=params.size,
            pages=pages,
        )


class TuplePaginator:
    """
    Specialized paginator for queries that return tuples of database objects.

    This is a convenience wrapper around ManualPaginator for the common case
    of joined queries that return tuples of model instances.
    """

    @staticmethod
    def tuple_transformer(row: Row) -> Tuple[Any, ...]:
        """Convert SQLAlchemy Row to tuple"""
        return tuple(row)

    @staticmethod
    async def paginate_tuples(
        session: AsyncSession, query: Select, params: Optional[Params] = None
    ) -> Page[Tuple[Any, ...]]:
        """
        Paginate a query that returns tuples of database objects.

        Args:
            session: AsyncSession for database operations
            query: SQLAlchemy Select query that returns multiple columns/tables
            params: Pagination parameters (page, size)

        Returns:
            Page[Tuple[Any, ...]] with tuple results

        Example:
            query = select(Activity, ActivityRecord).outerjoin(...)
            page = await TuplePaginator.paginate_tuples(session, query)
            # Returns: Page[Tuple[Activity, Optional[ActivityRecord]]]
        """

        return await ManualPaginator.paginate(
            session=session,
            query=query,
            params=params,
            result_transformer=TuplePaginator.tuple_transformer,
        )


# Convenience functions for common use cases
async def paginate_query(
    session: AsyncSession, query: Select, params: Optional[Params] = None
) -> Page[Tuple[Any, ...]]:
    """
    Convenience function for paginating queries with tuple results.

    This is the most common use case for manual pagination - queries that
    return tuples due to joins or multiple selected columns.

    Args:
        session: AsyncSession for database operations
        query: SQLAlchemy Select query
        params: Pagination parameters

    Returns:
        Page with tuple results
    """
    return await TuplePaginator.paginate_tuples(session, query, params)


async def paginate_with_transform(
    session: AsyncSession,
    query: Select,
    transformer: Callable,
    params: Optional[Params] = None,
) -> Page[T]:
    """
    Convenience function for paginating queries with custom transformation.

    Args:
        session: AsyncSession for database operations
        query: SQLAlchemy Select query
        transformer: Function to transform each row
        params: Pagination parameters

    Returns:
        Page with transformed results
    """
    return await ManualPaginator.paginate(
        session=session, query=query, params=params, result_transformer=transformer
    )
